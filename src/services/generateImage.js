const { openai } = require("@ai-sdk/openai");
const { experimental_generateImage: generateImage } = require("ai");
const logger = require("../utils/logger");

/**
 * Generates an image using OpenAI's image generation model
 * @param {string} prompt - The image generation prompt
 * @returns {Promise<Buffer>} - The generated image as a Buffer
 */
const generateImageFromText = async (prompt) => {
  if (!process.env.OPENAI_API_KEY) {
    throw new Error(
      "OpenAI API key is not set. Please set OPENAI_API_KEY in your .env file."
    );
  }

  try {
    logger.info(`Generating image with prompt: "${prompt}"`);

    const result = await generateImage({
      model: openai.image("imagen-3.0-generate-002"),
      prompt: prompt,
      size: "1024x1024",
    });

    // Access the generated image data directly
    const imageBuffer = Buffer.from(result.image.uint8Array);

    logger.success("Image generated successfully!");
    return imageBuffer;
  } catch (error) {
    logger.error("Error generating image:", error);
    throw new Error("Failed to generate image. Please try again later.");
  }
};

module.exports = {
  generateImageFromText,
};
